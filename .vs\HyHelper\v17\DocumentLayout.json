{"Version": 1, "WorkspaceRootPath": "D:\\HyDevelop\\HyHelper\\HyHelper\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\示例\\基础使用示例.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\示例\\基础使用示例.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\文档\\最佳实践指南.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\文档\\最佳实践指南.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\文档\\api使用文档.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\文档\\api使用文档.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\技术手册-flurl.http和cefsharp.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\技术手册-flurl.http和cefsharp.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\环境配置和依赖.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\环境配置和依赖.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\方法变量协调字典.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\方法变量协调字典.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebstorage存储管理流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebstorage存储管理流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebsimulationbrowser模拟操作流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebsimulationbrowser模拟操作流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebsessionmanager会话管理流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebsessionmanager会话管理流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebmodels数据模型流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebmodels数据模型流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebloginbrowser登录认证流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebloginbrowser登录认证流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebhelper辅助类流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebhelper辅助类流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebfileuploader文件上传流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebfileuploader文件上传流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|d:\\hydevelop\\hyhelper\\hyhelper\\extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebclient主客户端流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{163811E3-BCFB-4E4D-9D21-59068851D958}|ExtensionsTools\\ExtensionsTools.csproj|solutionrelative:extensionstools\\extensionstools\\etwebautomation\\开发指引文件\\etwebclient主客户端流程.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 243, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{2d7728c2-de0a-45b5-99aa-89b609dfde73}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "基础使用示例.cs", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\示例\\基础使用示例.cs", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\示例\\基础使用示例.cs", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\示例\\基础使用示例.cs", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\示例\\基础使用示例.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-03T08:05:54.437Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "最佳实践指南.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\最佳实践指南.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\最佳实践指南.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\最佳实践指南.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\最佳实践指南.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwwAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:05:47.22Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "API使用文档.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\API使用文档.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\API使用文档.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\API使用文档.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\文档\\API使用文档.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvxAAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:05:29.346Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "技术手册-Flurl.Http和CefSharp.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\技术手册-Flurl.Http和CefSharp.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\技术手册-Flurl.Http和CefSharp.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\技术手册-Flurl.Http和CefSharp.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\技术手册-Flurl.Http和CefSharp.md", "ViewState": "AgIAAI0CAAAAAAAAAAAQwIkCAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:05:15.458Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "环境配置和依赖.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\环境配置和依赖.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\环境配置和依赖.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\环境配置和依赖.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\环境配置和依赖.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:05:10.381Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "方法变量协调字典.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\方法变量协调字典.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\方法变量协调字典.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\方法变量协调字典.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\方法变量协调字典.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:05:06.212Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "README.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\README.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\README.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\README.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\README.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:05:00.919Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ETWebStorage存储管理流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebStorage存储管理流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebStorage存储管理流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebStorage存储管理流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebStorage存储管理流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:56.443Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ETWebSimulationBrowser模拟操作流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSimulationBrowser模拟操作流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSimulationBrowser模拟操作流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSimulationBrowser模拟操作流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSimulationBrowser模拟操作流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:52.435Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ETWebSessionManager会话管理流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSessionManager会话管理流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSessionManager会话管理流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSessionManager会话管理流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebSessionManager会话管理流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:48.195Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ETWebModels数据模型流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebModels数据模型流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebModels数据模型流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebModels数据模型流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebModels数据模型流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:42.986Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "ETWebLoginBrowser登录认证流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebLoginBrowser登录认证流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebLoginBrowser登录认证流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebLoginBrowser登录认证流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebLoginBrowser登录认证流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:36.762Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ETWebHelper辅助类流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebHelper辅助类流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebHelper辅助类流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebHelper辅助类流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebHelper辅助类流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:32.218Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ETWebFileUploader文件上传流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebFileUploader文件上传流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebFileUploader文件上传流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebFileUploader文件上传流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebFileUploader文件上传流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:27.059Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "ETWebClient主客户端流程.md", "DocumentMoniker": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebClient主客户端流程.md", "RelativeDocumentMoniker": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebClient主客户端流程.md", "ToolTip": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebClient主客户端流程.md", "RelativeToolTip": "ExtensionsTools\\ExtensionsTools\\ETWebAutomation\\开发指引文件\\ETWebClient主客户端流程.md", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-08-03T08:04:16.474Z", "EditorCaption": ""}]}, {"DockedHeight": 182, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{004be353-6879-467c-9d1e-9ac23cdf6d49}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1a46fd64-28d5-434c-8eb3-17a02d419b53}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{5a4e9529-b6a0-46b5-be4f-0f0b239bc0eb}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{a80febb4-e7e0-4147-b476-21aaf2453969}"}]}, {"DockedHeight": 133, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{0ad07096-bba9-4900-a651-0598d26f6d24}"}, {"$type": "Bookmark", "Name": "ST:0:0:{eefa5220-e298-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:1:0:{d212f56b-c48a-434c-a121-1c5d80b59b9f}"}]}]}]}